import numpy as np
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import Conv2D, MaxPooling2D, Flatten, Dense, Dropout, BatchNormalization
from tensorflow.keras.preprocessing.image import ImageDataGenerator
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau, ModelCheckpoint
from tensorflow.keras.optimizers import Adam
import os
from pathlib import Path
import matplotlib.pyplot as plt

class ECGImageTrainer:
    def __init__(self, data_path="ECG_Image_data", img_size=(224, 224), batch_size=32):
        """
        Initialize ECG Image Trainer
        
        Args:
            data_path (str): Path to ECG image dataset
            img_size (tuple): Target image size
            batch_size (int): Training batch size
        """
        self.data_path = data_path
        self.img_size = img_size
        self.batch_size = batch_size
        self.model = None
        self.history = None
        
        # ECG class information
        self.class_names = ['F', 'M', 'N', 'Q', 'S', 'V']
        self.class_descriptions = {
            'F': 'Fusion Beat',
            'M': 'Myocardial Infarction', 
            'N': 'Normal Beat',
            'Q': 'Q-wave',
            'S': 'Supraventricular Ectopic Beat',
            'V': 'Ventricular Ectopic Beat'
        }
        
        print(f"🫀 ECG Image Trainer Initialized")
        print(f"📁 Data path: {self.data_path}")
        print(f"🖼️  Image size: {self.img_size}")
        print(f"📦 Batch size: {self.batch_size}")
    
    def setup_data_generators(self):
        """Setup data generators for training and validation"""
        
        # Data augmentation for training
        train_datagen = ImageDataGenerator(
            rescale=1./255,
            rotation_range=10,
            width_shift_range=0.1,
            height_shift_range=0.1,
            shear_range=0.1,
            zoom_range=0.1,
            horizontal_flip=False,  # ECG signals shouldn't be flipped
            fill_mode='nearest',
            validation_split=0.2  # 20% for validation
        )
        
        # Only rescaling for validation
        val_datagen = ImageDataGenerator(
            rescale=1./255,
            validation_split=0.2
        )
        
        train_path = os.path.join(self.data_path, 'train')
        
        if not os.path.exists(train_path):
            raise ValueError(f"Training data path not found: {train_path}")
        
        # Training generator
        self.train_generator = train_datagen.flow_from_directory(
            train_path,
            target_size=self.img_size,
            batch_size=self.batch_size,
            class_mode='categorical',
            subset='training',
            shuffle=True
        )
        
        # Validation generator
        self.val_generator = val_datagen.flow_from_directory(
            train_path,
            target_size=self.img_size,
            batch_size=self.batch_size,
            class_mode='categorical',
            subset='validation',
            shuffle=False
        )
        
        print(f"✅ Data generators created")
        print(f"📊 Training samples: {self.train_generator.samples}")
        print(f"📊 Validation samples: {self.val_generator.samples}")
        print(f"🏷️  Classes found: {list(self.train_generator.class_indices.keys())}")
        
        return self.train_generator, self.val_generator
    
    def create_cnn_model(self):
        """Create CNN model for ECG image classification"""
        
        model = Sequential([
            # First Convolutional Block
            Conv2D(32, (3, 3), activation='relu', input_shape=(*self.img_size, 3)),
            BatchNormalization(),
            MaxPooling2D(2, 2),
            
            # Second Convolutional Block
            Conv2D(64, (3, 3), activation='relu'),
            BatchNormalization(),
            MaxPooling2D(2, 2),
            
            # Third Convolutional Block
            Conv2D(128, (3, 3), activation='relu'),
            BatchNormalization(),
            MaxPooling2D(2, 2),
            
            # Fourth Convolutional Block
            Conv2D(256, (3, 3), activation='relu'),
            BatchNormalization(),
            MaxPooling2D(2, 2),
            
            # Flatten and Dense layers
            Flatten(),
            Dropout(0.5),
            Dense(512, activation='relu'),
            BatchNormalization(),
            Dropout(0.3),
            Dense(256, activation='relu'),
            Dropout(0.2),
            Dense(len(self.class_names), activation='softmax')
        ])
        
        # Compile model
        model.compile(
            optimizer=Adam(learning_rate=0.001),
            loss='categorical_crossentropy',
            metrics=['accuracy', 'top_2_accuracy']
        )
        
        self.model = model
        
        print("✅ CNN Model created")
        print(f"📊 Model summary:")
        model.summary()
        
        return model
    
    def train_model(self, epochs=50, save_path="ecg_image_model.h5"):
        """
        Train the CNN model
        
        Args:
            epochs (int): Number of training epochs
            save_path (str): Path to save the trained model
        """
        
        if self.model is None:
            self.create_cnn_model()
        
        if not hasattr(self, 'train_generator'):
            self.setup_data_generators()
        
        # Callbacks
        callbacks = [
            EarlyStopping(
                monitor='val_loss',
                patience=10,
                restore_best_weights=True,
                verbose=1
            ),
            ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.2,
                patience=5,
                min_lr=1e-7,
                verbose=1
            ),
            ModelCheckpoint(
                save_path,
                monitor='val_accuracy',
                save_best_only=True,
                verbose=1
            )
        ]
        
        print(f"🚀 Starting training for {epochs} epochs...")
        
        # Train the model
        self.history = self.model.fit(
            self.train_generator,
            epochs=epochs,
            validation_data=self.val_generator,
            callbacks=callbacks,
            verbose=1
        )
        
        print(f"✅ Training completed!")
        print(f"💾 Model saved to: {save_path}")
        
        return self.history
    
    def plot_training_history(self):
        """Plot training history"""
        if self.history is None:
            print("❌ No training history available")
            return
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))
        
        # Plot accuracy
        ax1.plot(self.history.history['accuracy'], label='Training Accuracy')
        ax1.plot(self.history.history['val_accuracy'], label='Validation Accuracy')
        ax1.set_title('Model Accuracy')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Accuracy')
        ax1.legend()
        ax1.grid(True)
        
        # Plot loss
        ax2.plot(self.history.history['loss'], label='Training Loss')
        ax2.plot(self.history.history['val_loss'], label='Validation Loss')
        ax2.set_title('Model Loss')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('Loss')
        ax2.legend()
        ax2.grid(True)
        
        plt.tight_layout()
        plt.savefig('training_history.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("📊 Training history plotted and saved as 'training_history.png'")
    
    def evaluate_model(self):
        """Evaluate the trained model"""
        if self.model is None:
            print("❌ No model available for evaluation")
            return
        
        if not hasattr(self, 'val_generator'):
            print("❌ No validation data available")
            return
        
        print("📊 Evaluating model on validation data...")
        
        # Evaluate on validation data
        val_loss, val_accuracy, val_top2_acc = self.model.evaluate(
            self.val_generator,
            verbose=1
        )
        
        print(f"\n📈 Validation Results:")
        print(f"   Loss: {val_loss:.4f}")
        print(f"   Accuracy: {val_accuracy:.4f} ({val_accuracy*100:.2f}%)")
        print(f"   Top-2 Accuracy: {val_top2_acc:.4f} ({val_top2_acc*100:.2f}%)")
        
        return val_loss, val_accuracy, val_top2_acc


def main():
    """Main function to train ECG image classification model"""
    print("🫀 ECG Image Model Training")
    print("="*50)
    
    # Initialize trainer
    trainer = ECGImageTrainer(
        data_path="ECG_Image_data",
        img_size=(224, 224),
        batch_size=32
    )
    
    try:
        # Setup data
        trainer.setup_data_generators()
        
        # Create model
        trainer.create_cnn_model()
        
        # Train model
        trainer.train_model(epochs=30, save_path="ecg_image_model.h5")
        
        # Evaluate model
        trainer.evaluate_model()
        
        # Plot training history
        trainer.plot_training_history()
        
        print("\n✅ Training completed successfully!")
        print("🎯 You can now use 'predict_image.py' with the trained model")
        
    except Exception as e:
        print(f"❌ Training failed: {e}")
        print("💡 Make sure the ECG_Image_data folder exists with train/test subdirectories")


if __name__ == "__main__":
    main()
