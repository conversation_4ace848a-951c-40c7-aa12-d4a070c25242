# # main.py

# import pandas as pd
# from sklearn.model_selection import train_test_split
# from sklearn.ensemble import RandomForestClassifier
# from sklearn.metrics import classification_report, accuracy_score
# from sklearn.preprocessing import StandardScaler

# # Load MIT-BIH dataset
# train_df = pd.read_csv("mitbih_train.csv", header=None)
# test_df = pd.read_csv("mitbih_test.csv", header=None)

# # Combine for consistent preprocessing
# df = pd.concat([train_df, test_df])

# # Split features and labels
# X = df.iloc[:, :-1].values
# y = df.iloc[:, -1].values

# # Normalize features
# scaler = StandardScaler()
# X_scaled = scaler.fit_transform(X)

# # Split into train/val
# X_train, X_val, y_train, y_val = train_test_split(X_scaled, y, test_size=0.2, random_state=42, stratify=y)

# # Train model
# model = RandomForestClassifier(n_estimators=100, random_state=42)
# model.fit(X_train, y_train)

# # Predict & Evaluate
# y_pred = model.predict(X_val)

# print("🎯 Accuracy:", accuracy_score(y_val, y_pred))
# print("📊 Classification Report:\n", classification_report(y_val, y_pred))















import pandas as pd
import numpy as np
import tensorflow as tf
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import Dense, Conv1D, GlobalMaxPooling1D, Dropout
from sklearn.metrics import classification_report

# 🔹 Load CSV dataset
df1 = pd.read_csv("ptbdb_normal.csv", header=None)
df2 = pd.read_csv("ptbdb_abnormal.csv", header=None)
df = pd.concat([df1, df2])  # Combine both normal and abnormal

# 🔹 Split features and labels
X = df.iloc[:, :-1].values  # ECG signals
y = df.iloc[:, -1].values   # Labels: 0 (normal), 1 (abnormal)

# 🔹 Train/test split
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

# 🔹 Normalize the data
scaler = StandardScaler()
X_train = scaler.fit_transform(X_train)
X_test = scaler.transform(X_test)

# 🔹 Reshape for Conv1D [samples, time_steps, channels]
X_train = X_train[..., np.newaxis]
X_test = X_test[..., np.newaxis]

# 🧠 Build 1D CNN Model
model = Sequential([
    Conv1D(64, 3, activation='relu', input_shape=(X_train.shape[1], 1)),
    Dropout(0.2),
    Conv1D(128, 3, activation='relu'),
    GlobalMaxPooling1D(),
    Dense(64, activation='relu'),
    Dropout(0.3),
    Dense(1, activation='sigmoid')
])

model.compile(optimizer='adam', loss='binary_crossentropy', metrics=['accuracy'])

# 🚀 Train the model
model.fit(X_train, y_train, epochs=10, validation_split=0.1, batch_size=64)

# 📈 Evaluate
y_pred = (model.predict(X_test) > 0.5).astype("int32")
print(classification_report(y_test, y_pred))

# 💾 Save model
model.save("ecg_signal_model.h5")
print("✅ Model saved as ecg_signal_model.h5")
