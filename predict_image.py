import numpy as np
import tensorflow as tf
from tensorflow.keras.models import load_model
from tensorflow.keras.preprocessing import image
import os
import cv2
from pathlib import Path

class ECGImagePredictor:
    def __init__(self, model_path=None):
        """
        Initialize ECG Image Predictor
        
        Args:
            model_path (str): Path to trained model. If None, will create a simple model.
        """
        self.model = None
        self.class_names = ['F', 'M', 'N', 'Q', 'S', 'V']  # ECG classes
        self.class_descriptions = {
            'F': 'Fusion Beat',
            'M': 'Myocardial Infarction', 
            'N': 'Normal Beat',
            'Q': 'Q-wave',
            'S': 'Supraventricular Ectopic Beat',
            'V': 'Ventricular Ectopic Beat'
        }
        
        if model_path and os.path.exists(model_path):
            self.load_model(model_path)
        else:
            print("⚠️  No pre-trained model found. You'll need to train a model first.")
            print("Creating a simple CNN architecture for demonstration...")
            self.create_simple_model()
    
    def create_simple_model(self):
        """Create a simple CNN model for ECG image classification"""
        model = tf.keras.Sequential([
            tf.keras.layers.Conv2D(32, (3, 3), activation='relu', input_shape=(224, 224, 3)),
            tf.keras.layers.MaxPooling2D(2, 2),
            tf.keras.layers.Conv2D(64, (3, 3), activation='relu'),
            tf.keras.layers.MaxPooling2D(2, 2),
            tf.keras.layers.Conv2D(128, (3, 3), activation='relu'),
            tf.keras.layers.MaxPooling2D(2, 2),
            tf.keras.layers.Flatten(),
            tf.keras.layers.Dropout(0.5),
            tf.keras.layers.Dense(512, activation='relu'),
            tf.keras.layers.Dense(len(self.class_names), activation='softmax')
        ])
        
        model.compile(
            optimizer='adam',
            loss='categorical_crossentropy',
            metrics=['accuracy']
        )
        
        self.model = model
        print("✅ Simple CNN model created (untrained)")
    
    def load_model(self, model_path):
        """Load a pre-trained model"""
        try:
            self.model = load_model(model_path)
            print(f"✅ Model loaded from {model_path}")
        except Exception as e:
            print(f"❌ Error loading model: {e}")
            self.create_simple_model()
    
    def preprocess_image(self, image_path, target_size=(224, 224)):
        """
        Preprocess ECG image for prediction
        
        Args:
            image_path (str): Path to ECG image
            target_size (tuple): Target size for resizing
            
        Returns:
            np.array: Preprocessed image array
        """
        try:
            # Load image
            img = cv2.imread(image_path)
            if img is None:
                raise ValueError(f"Could not load image from {image_path}")
            
            # Convert BGR to RGB
            img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            
            # Resize image
            img = cv2.resize(img, target_size)
            
            # Normalize pixel values to [0, 1]
            img = img.astype(np.float32) / 255.0
            
            # Add batch dimension
            img = np.expand_dims(img, axis=0)
            
            return img
            
        except Exception as e:
            print(f"❌ Error preprocessing image: {e}")
            return None
    
    def predict_single_image(self, image_path):
        """
        Predict ECG class for a single image
        
        Args:
            image_path (str): Path to ECG image
            
        Returns:
            dict: Prediction results
        """
        if self.model is None:
            return {"error": "No model available"}
        
        # Preprocess image
        processed_img = self.preprocess_image(image_path)
        if processed_img is None:
            return {"error": "Failed to preprocess image"}
        
        try:
            # Make prediction
            predictions = self.model.predict(processed_img, verbose=0)
            predicted_class_idx = np.argmax(predictions[0])
            confidence = predictions[0][predicted_class_idx]
            predicted_class = self.class_names[predicted_class_idx]
            
            # Get all class probabilities
            class_probabilities = {}
            for i, class_name in enumerate(self.class_names):
                class_probabilities[class_name] = float(predictions[0][i])
            
            result = {
                "image_path": image_path,
                "predicted_class": predicted_class,
                "predicted_description": self.class_descriptions[predicted_class],
                "confidence": float(confidence),
                "all_probabilities": class_probabilities
            }
            
            return result
            
        except Exception as e:
            return {"error": f"Prediction failed: {e}"}
    
    def predict_batch_images(self, image_folder):
        """
        Predict ECG classes for all images in a folder
        
        Args:
            image_folder (str): Path to folder containing ECG images
            
        Returns:
            list: List of prediction results
        """
        results = []
        image_extensions = ['.png', '.jpg', '.jpeg', '.bmp', '.tiff']
        
        image_folder = Path(image_folder)
        if not image_folder.exists():
            return [{"error": f"Folder {image_folder} does not exist"}]
        
        # Find all image files
        image_files = []
        for ext in image_extensions:
            image_files.extend(image_folder.glob(f"*{ext}"))
            image_files.extend(image_folder.glob(f"*{ext.upper()}"))
        
        if not image_files:
            return [{"error": "No image files found in the folder"}]
        
        print(f"🔍 Found {len(image_files)} images to process...")
        
        for img_file in image_files:
            result = self.predict_single_image(str(img_file))
            results.append(result)
        
        return results
    
    def display_results(self, results):
        """Display prediction results in a formatted way"""
        if isinstance(results, dict):
            results = [results]
        
        for i, result in enumerate(results):
            print(f"\n{'='*60}")
            print(f"📊 PREDICTION RESULT #{i+1}")
            print(f"{'='*60}")
            
            if "error" in result:
                print(f"❌ Error: {result['error']}")
                continue
            
            print(f"📁 Image: {Path(result['image_path']).name}")
            print(f"🎯 Predicted Class: {result['predicted_class']}")
            print(f"📝 Description: {result['predicted_description']}")
            print(f"🔥 Confidence: {result['confidence']:.2%}")
            
            print(f"\n📈 All Class Probabilities:")
            for class_name, prob in result['all_probabilities'].items():
                desc = self.class_descriptions[class_name]
                print(f"   {class_name} ({desc}): {prob:.2%}")


def main():
    """Main function to demonstrate ECG image prediction"""
    print("🫀 ECG Image Classification System")
    print("="*50)
    
    # Initialize predictor
    predictor = ECGImagePredictor()
    
    # Example usage - you can modify these paths
    image_data_path = "ECG_Image_data"
    
    print(f"\n🔍 Available options:")
    print(f"1. Predict single image")
    print(f"2. Predict all images in a folder")
    print(f"3. Test with sample images from dataset")
    
    choice = input("\nEnter your choice (1-3): ").strip()
    
    if choice == "1":
        image_path = input("Enter path to ECG image: ").strip()
        if os.path.exists(image_path):
            result = predictor.predict_single_image(image_path)
            predictor.display_results(result)
        else:
            print(f"❌ Image not found: {image_path}")
    
    elif choice == "2":
        folder_path = input("Enter path to folder containing ECG images: ").strip()
        results = predictor.predict_batch_images(folder_path)
        predictor.display_results(results)
    
    elif choice == "3":
        # Test with sample images from each class
        test_folders = [
            f"{image_data_path}/test/N",  # Normal
            f"{image_data_path}/test/V",  # Ventricular
            f"{image_data_path}/test/S",  # Supraventricular
        ]
        
        for folder in test_folders:
            if os.path.exists(folder):
                print(f"\n🧪 Testing with images from {folder}")
                # Get first 3 images from folder
                image_files = list(Path(folder).glob("*.png"))[:3]
                for img_file in image_files:
                    result = predictor.predict_single_image(str(img_file))
                    predictor.display_results(result)
            else:
                print(f"⚠️  Test folder not found: {folder}")
    
    else:
        print("❌ Invalid choice")


if __name__ == "__main__":
    main()
