# import pandas as pd
# from tensorflow.keras.models import load_model
# from sklearn.preprocessing import StandardScaler
# import numpy as np

# # Load the model
# model = load_model("ecg_signal_model.h5")

# # Load user's ECG CSV (single row)
# df = pd.read_csv("user_ecg_data.csv", header=None)
# X = df.values

# # Normalize + reshape
# scaler = StandardScaler()
# X_scaled = scaler.fit_transform(X)
# X_scaled = X_scaled[..., np.newaxis]

# # Predict
# prediction = model.predict(X_scaled)[0][0]
# result = "🚨 Abnormal (Possible Issue)" if prediction > 0.5 else "✅ Normal"
# print("Prediction:", result)













import pandas as pd
from sklearn.preprocessing import StandardScaler
from tensorflow.keras.models import load_model

# Load the ECG data
data = pd.read_csv("user_ecg_data.csv")

# Drop columns that are not numeric or not needed for prediction
X = data.drop(columns=["id", "gender", "heart_attack"])  # Remove non-feature columns

# Scale the input features
scaler = StandardScaler()
X_scaled = scaler.fit_transform(X)

# Load the model
model = load_model("your_model_path.h5")  # Replace with your actual model path

# Predict
predictions = model.predict(X_scaled)

# Optional: interpret predictions
print("Predictions:", predictions)
