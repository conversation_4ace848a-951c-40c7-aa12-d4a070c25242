# 🫀 ECG Image Classification System

This system provides ECG (Electrocardiogram) image classification using deep learning. It can analyze ECG images and classify them into different cardiac conditions.

## 📁 Project Structure

```
ECG/
├── ECG_Image_data/           # ECG image dataset
│   ├── train/               # Training images
│   │   ├── F/              # Fusion Beat
│   │   ├── M/              # Myocardial Infarction
│   │   ├── N/              # Normal Beat
│   │   ├── Q/              # Q-wave
│   │   ├── S/              # Supraventricular Ectopic Beat
│   │   └── V/              # Ventricular Ectopic Beat
│   └── test/               # Test images (same structure)
├── predict_image.py         # Main prediction script (requires trained model)
├── train_image_model.py     # Training script for CNN model
├── demo_image_prediction.py # Demo script (no model required)
└── README_ECG_Image_Prediction.md
```

## 🏷️ ECG Classes

| Class | Description | Medical Significance |
|-------|-------------|---------------------|
| **N** | Normal Beat | Healthy heart rhythm |
| **V** | Ventricular Ectopic Beat | Irregular beat from lower heart chambers |
| **S** | Supraventricular Ectopic Beat | Irregular beat from upper heart chambers |
| **F** | Fusion Beat | Combination of normal and abnormal beats |
| **M** | Myocardial Infarction | Heart attack related patterns |
| **Q** | Q-wave | Specific ECG wave pattern |

## 🚀 Quick Start

### Option 1: Demo Mode (No Training Required)

Run the demo script to analyze ECG images using basic image processing:

```bash
python demo_image_prediction.py
```

**Features:**
- ✅ Works immediately without training
- ✅ Basic image feature analysis
- ✅ Rule-based classification
- ⚠️ Not medically accurate (demo purposes only)

### Option 2: Train and Use ML Model

#### Step 1: Train the Model

```bash
python train_image_model.py
```

This will:
- Load ECG images from `ECG_Image_data/train/`
- Train a CNN model
- Save the trained model as `ecg_image_model.h5`
- Generate training plots

#### Step 2: Use Trained Model for Prediction

```bash
python predict_image.py
```

**Features:**
- ✅ Uses trained CNN model
- ✅ High accuracy predictions
- ✅ Confidence scores
- ✅ Batch processing support

## 📊 Usage Examples

### Single Image Prediction

```python
from predict_image import ECGImagePredictor

# Initialize predictor with trained model
predictor = ECGImagePredictor("ecg_image_model.h5")

# Predict single image
result = predictor.predict_single_image("path/to/ecg_image.png")
print(f"Predicted: {result['predicted_class']} ({result['confidence']:.2%})")
```

### Batch Prediction

```python
# Predict all images in a folder
results = predictor.predict_batch_images("ECG_Image_data/test/N")
predictor.display_results(results)
```

### Demo Analysis

```python
from demo_image_prediction import ECGImageDemo

# Initialize demo
demo = ECGImageDemo()

# Analyze image features
result = demo.analyze_image_features("path/to/ecg_image.png")
demo.display_results(result)
```

## 🛠️ Installation Requirements

```bash
pip install tensorflow opencv-python numpy matplotlib pathlib
```

## 📈 Model Architecture

The CNN model includes:
- **4 Convolutional blocks** with BatchNormalization
- **MaxPooling layers** for dimensionality reduction
- **Dropout layers** for regularization
- **Dense layers** for classification
- **Softmax output** for 6-class classification

## 🎯 Performance Features

### Training Features:
- Data augmentation (rotation, shifts, zoom)
- Early stopping to prevent overfitting
- Learning rate reduction on plateau
- Model checkpointing (saves best model)

### Prediction Features:
- Image preprocessing and normalization
- Confidence scores for predictions
- Batch processing capabilities
- Detailed result reporting

## 📝 Example Output

```
======================================================================
📊 PREDICTION RESULT #1
======================================================================
📁 Image: N46791.png
🎯 Predicted Class: N
📝 Description: Normal Beat - Healthy heart rhythm
🔥 Confidence: 94.23%

📈 All Class Probabilities:
   N (Normal Beat): 94.23%
   V (Ventricular Ectopic Beat): 3.45%
   S (Supraventricular Ectopic Beat): 1.12%
   F (Fusion Beat): 0.89%
   M (Myocardial Infarction): 0.21%
   Q (Q-wave): 0.10%
```

## ⚠️ Important Notes

1. **Medical Disclaimer**: This system is for educational/research purposes only. It should NOT be used for actual medical diagnosis.

2. **Data Requirements**: Ensure your ECG images are in the correct folder structure for training.

3. **Model Training**: Training requires significant computational resources and time (30+ minutes on CPU).

4. **Image Format**: Supports PNG, JPG, JPEG, BMP, TIFF formats.

## 🔧 Customization

### Modify Image Size
```python
trainer = ECGImageTrainer(img_size=(256, 256))  # Default: (224, 224)
```

### Adjust Training Parameters
```python
trainer.train_model(epochs=50, save_path="my_model.h5")
```

### Change Batch Size
```python
trainer = ECGImageTrainer(batch_size=64)  # Default: 32
```

## 🐛 Troubleshooting

### Common Issues:

1. **"No module named 'cv2'"**
   ```bash
   pip install opencv-python
   ```

2. **"Training data path not found"**
   - Ensure `ECG_Image_data/train/` exists
   - Check folder structure matches expected format

3. **"Could not load image"**
   - Verify image file exists and is not corrupted
   - Check supported formats (PNG, JPG, etc.)

4. **Low training accuracy**
   - Increase training epochs
   - Adjust learning rate
   - Add more training data

## 📞 Support

For issues or questions:
1. Check the troubleshooting section
2. Verify your data structure matches the expected format
3. Ensure all dependencies are installed correctly

## 🎉 Next Steps

1. **Collect more data**: Add more ECG images to improve model accuracy
2. **Experiment with architectures**: Try different CNN architectures
3. **Add preprocessing**: Implement advanced image preprocessing techniques
4. **Deploy model**: Create a web interface for easy access
