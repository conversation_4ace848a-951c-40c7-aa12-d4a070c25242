import numpy as np
import cv2
import os
from pathlib import Path
import random

class ECGImageDemo:
    def __init__(self, data_path="ECG_Image_data"):
        """
        Demo ECG Image Analysis (without trained model)
        
        Args:
            data_path (str): Path to ECG image dataset
        """
        self.data_path = data_path
        self.class_names = ['F', 'M', 'N', 'Q', 'S', 'V']
        self.class_descriptions = {
            'F': 'Fusion Beat - Combination of normal and abnormal beats',
            'M': 'Myocardial Infarction - Heart attack related patterns', 
            'N': 'Normal Beat - Healthy heart rhythm',
            'Q': 'Q-wave - Specific ECG wave pattern',
            'S': 'Supraventricular Ectopic Beat - Irregular beat from upper chambers',
            'V': 'Ventricular Ectopic Beat - Irregular beat from lower chambers'
        }
        
        print(f"🫀 ECG Image Demo System")
        print(f"📁 Data path: {self.data_path}")
    
    def analyze_image_features(self, image_path):
        """
        Analyze basic image features (without ML model)
        
        Args:
            image_path (str): Path to ECG image
            
        Returns:
            dict: Analysis results
        """
        try:
            # Load image
            img = cv2.imread(image_path)
            if img is None:
                return {"error": f"Could not load image: {image_path}"}
            
            # Convert to grayscale for analysis
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            
            # Basic image statistics
            height, width = gray.shape
            mean_intensity = np.mean(gray)
            std_intensity = np.std(gray)
            min_intensity = np.min(gray)
            max_intensity = np.max(gray)
            
            # Edge detection to analyze signal complexity
            edges = cv2.Canny(gray, 50, 150)
            edge_density = np.sum(edges > 0) / (height * width)
            
            # Horizontal line detection (ECG baseline)
            horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (25, 1))
            horizontal_lines = cv2.morphologyEx(edges, cv2.MORPH_OPEN, horizontal_kernel)
            baseline_strength = np.sum(horizontal_lines > 0) / (height * width)
            
            # Vertical variations (QRS complexes, etc.)
            vertical_gradient = np.abs(np.diff(gray, axis=0))
            vertical_variation = np.mean(vertical_gradient)
            
            # Simple heuristic classification based on features
            predicted_class = self.simple_classification(
                edge_density, baseline_strength, vertical_variation, mean_intensity
            )
            
            result = {
                "image_path": image_path,
                "image_size": f"{width}x{height}",
                "mean_intensity": float(mean_intensity),
                "std_intensity": float(std_intensity),
                "intensity_range": f"{min_intensity}-{max_intensity}",
                "edge_density": float(edge_density),
                "baseline_strength": float(baseline_strength),
                "vertical_variation": float(vertical_variation),
                "predicted_class": predicted_class,
                "predicted_description": self.class_descriptions[predicted_class],
                "confidence": "Demo Mode - No ML Model"
            }
            
            return result
            
        except Exception as e:
            return {"error": f"Analysis failed: {e}"}
    
    def simple_classification(self, edge_density, baseline_strength, vertical_variation, mean_intensity):
        """
        Simple rule-based classification (demo purposes only)
        
        Args:
            edge_density (float): Density of edges in image
            baseline_strength (float): Strength of horizontal baseline
            vertical_variation (float): Amount of vertical variation
            mean_intensity (float): Mean pixel intensity
            
        Returns:
            str: Predicted class
        """
        # Simple heuristic rules (not medically accurate - for demo only!)
        
        if edge_density > 0.15 and vertical_variation > 30:
            # High complexity might indicate abnormal patterns
            if baseline_strength > 0.02:
                return 'V'  # Ventricular
            else:
                return 'S'  # Supraventricular
        
        elif edge_density < 0.08 and vertical_variation < 20:
            # Low complexity might indicate normal or fusion
            if mean_intensity > 200:
                return 'N'  # Normal (often brighter background)
            else:
                return 'F'  # Fusion
        
        elif vertical_variation > 25:
            return 'M'  # Myocardial infarction patterns
        
        else:
            return 'Q'  # Q-wave patterns
    
    def analyze_folder(self, folder_path, max_images=5):
        """
        Analyze multiple images in a folder
        
        Args:
            folder_path (str): Path to folder containing images
            max_images (int): Maximum number of images to analyze
            
        Returns:
            list: List of analysis results
        """
        results = []
        image_extensions = ['.png', '.jpg', '.jpeg', '.bmp', '.tiff']
        
        folder_path = Path(folder_path)
        if not folder_path.exists():
            return [{"error": f"Folder {folder_path} does not exist"}]
        
        # Find image files
        image_files = []
        for ext in image_extensions:
            image_files.extend(folder_path.glob(f"*{ext}"))
            image_files.extend(folder_path.glob(f"*{ext.upper()}"))
        
        if not image_files:
            return [{"error": "No image files found in the folder"}]
        
        # Randomly sample images if there are too many
        if len(image_files) > max_images:
            image_files = random.sample(image_files, max_images)
        
        print(f"🔍 Analyzing {len(image_files)} images from {folder_path.name}...")
        
        for img_file in image_files:
            result = self.analyze_image_features(str(img_file))
            results.append(result)
        
        return results
    
    def display_results(self, results):
        """Display analysis results"""
        if isinstance(results, dict):
            results = [results]
        
        for i, result in enumerate(results):
            print(f"\n{'='*70}")
            print(f"📊 ANALYSIS RESULT #{i+1}")
            print(f"{'='*70}")
            
            if "error" in result:
                print(f"❌ Error: {result['error']}")
                continue
            
            print(f"📁 Image: {Path(result['image_path']).name}")
            print(f"📏 Size: {result['image_size']}")
            print(f"🎯 Predicted Class: {result['predicted_class']}")
            print(f"📝 Description: {result['predicted_description']}")
            print(f"🔥 Confidence: {result['confidence']}")
            
            print(f"\n📈 Image Features:")
            print(f"   Mean Intensity: {result['mean_intensity']:.2f}")
            print(f"   Std Intensity: {result['std_intensity']:.2f}")
            print(f"   Intensity Range: {result['intensity_range']}")
            print(f"   Edge Density: {result['edge_density']:.4f}")
            print(f"   Baseline Strength: {result['baseline_strength']:.4f}")
            print(f"   Vertical Variation: {result['vertical_variation']:.2f}")
    
    def demo_all_classes(self):
        """Demo analysis on sample images from each class"""
        print(f"\n🧪 DEMO: Analyzing sample images from each ECG class")
        print(f"{'='*70}")
        
        for class_name in self.class_names:
            class_folder = Path(self.data_path) / "train" / class_name
            
            if class_folder.exists():
                print(f"\n🔍 Analyzing class '{class_name}' - {self.class_descriptions[class_name]}")
                results = self.analyze_folder(str(class_folder), max_images=2)
                self.display_results(results)
            else:
                print(f"⚠️  Class folder not found: {class_folder}")


def main():
    """Main demo function"""
    print("🫀 ECG Image Analysis Demo")
    print("="*50)
    print("⚠️  Note: This is a demo using basic image analysis.")
    print("   For accurate medical diagnosis, use a trained ML model.")
    print("="*50)
    
    # Initialize demo
    demo = ECGImageDemo()
    
    print(f"\n🔍 Available options:")
    print(f"1. Analyze single image")
    print(f"2. Analyze images in a folder")
    print(f"3. Demo all ECG classes")
    print(f"4. Quick test with sample images")
    
    choice = input("\nEnter your choice (1-4): ").strip()
    
    if choice == "1":
        image_path = input("Enter path to ECG image: ").strip()
        if os.path.exists(image_path):
            result = demo.analyze_image_features(image_path)
            demo.display_results(result)
        else:
            print(f"❌ Image not found: {image_path}")
    
    elif choice == "2":
        folder_path = input("Enter path to folder containing ECG images: ").strip()
        max_images = input("Max images to analyze (default 5): ").strip()
        max_images = int(max_images) if max_images.isdigit() else 5
        
        results = demo.analyze_folder(folder_path, max_images)
        demo.display_results(results)
    
    elif choice == "3":
        demo.demo_all_classes()
    
    elif choice == "4":
        # Quick test with some sample images
        test_paths = [
            "ECG_Image_data/train/N",  # Normal
            "ECG_Image_data/train/V",  # Ventricular
        ]
        
        for path in test_paths:
            if os.path.exists(path):
                print(f"\n🧪 Quick test with {path}")
                results = demo.analyze_folder(path, max_images=1)
                demo.display_results(results)
    
    else:
        print("❌ Invalid choice")
    
    print(f"\n💡 To train a proper ML model, run: python train_image_model.py")
    print(f"💡 To use a trained model for prediction, run: python predict_image.py")


if __name__ == "__main__":
    main()
